#!/bin/bash
# debug_php.sh

# 获取传入的文件路径
FILE_PATH="$1"

# 获取项目根目录
PROJECT_ROOT="/Users/<USER>/Codes/www/social_media_data"

# 计算相对路径
RELATIVE_PATH=${FILE_PATH#$PROJECT_ROOT/}

# 容器内的完整路径
CONTAINER_PATH="/var/www/html/$RELATIVE_PATH"

echo "Debugging file: $CONTAINER_PATH"

# 执行调试，设置 Xdebug 环境变量
# 请将 your_container_name 替换为实际容器名
docker exec -i social_media_data_web_dev php \
    -dxdebug.mode=debug \
    -dxdebug.client_host=host.docker.internal \
    -dxdebug.client_port=9003 \
    -dxdebug.start_with_request=yes \
    "$CONTAINER_PATH"