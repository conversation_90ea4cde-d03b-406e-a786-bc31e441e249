<?php if(!defined("RUN_MODE")) die();?>
<?php
/**
 * The model file of smdata module of chanzhiEPS.
 *
 * @license     ZPLV1.2 (http://zpl.pub/page/zplv12.html)
 * <AUTHOR> Generated
 * @package     smdata
 * @version     $Id$
 * @link        http://www.bestvee.com
 */

class smdataModel extends model
{
    /**
     * Get WeChat API interface time span limits.
     *
     * @access public
     * @return array
     */
    public function getApiTimeLimits()
    {
        return array(
            'user_summary' => array('maxDays' => 7, 'sameDateRequired' => false),
            'user_cumulate' => array('maxDays' => 7, 'sameDateRequired' => false),
            'article_summary' => array('maxDays' => 1, 'sameDateRequired' => true),
            'article_total' => array('maxDays' => 1, 'sameDateRequired' => true),
            'user_read' => array('maxDays' => 3, 'sameDateRequired' => false),
            'user_read_hour' => array('maxDays' => 1, 'sameDateRequired' => true),
            'user_share' => array('maxDays' => 7, 'sameDateRequired' => false),
            'user_share_hour' => array('maxDays' => 1, 'sameDateRequired' => true),
            'upstream_msg' => array('maxDays' => 7, 'sameDateRequired' => false),
            'upstream_msg_hourly' => array('maxDays' => 1, 'sameDateRequired' => true),
            'upstream_msg_weekly' => array('maxDays' => 30, 'sameDateRequired' => false),
            'upstream_msg_monthly' => array('maxDays' => 30, 'sameDateRequired' => false),
            'upstream_msg_dist_summary' => array('maxDays' => 15, 'sameDateRequired' => false),
            'upstream_msg_dist_weekly' => array('maxDays' => 30, 'sameDateRequired' => false),
            'upstream_msg_dist_monthly' => array('maxDays' => 30, 'sameDateRequired' => false)
        );
    }

    /**
     * Validate date range for specific API interface.
     *
     * @param  string $interfaceType
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return array
     */
    public function validateDateRange($interfaceType, $startDate, $endDate)
    {
        $limits = $this->getApiTimeLimits();

        if(!isset($limits[$interfaceType]))
        {
            return array('result' => false, 'message' => "Unknown interface type: $interfaceType");
        }

        $limit = $limits[$interfaceType];

        // Validate date format
        if(!$this->isValidDate($startDate) || !$this->isValidDate($endDate))
        {
            return array('result' => false, 'message' => 'Invalid date format. Use YYYY-MM-DD.');
        }

        // For interfaces requiring same date
        if($limit['sameDateRequired'] && $startDate !== $endDate)
        {
            return array('result' => false, 'message' => "Interface $interfaceType requires begin_date and end_date to be the same.");
        }

        // Check time span
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / 86400;
        if($daysDiff > $limit['maxDays'])
        {
            return array('result' => false, 'message' => "Time span exceeds maximum limit of {$limit['maxDays']} days for interface $interfaceType.");
        }

        if($daysDiff < 0)
        {
            return array('result' => false, 'message' => 'End date cannot be earlier than start date.');
        }

        return array('result' => true, 'message' => 'Date range is valid.');
    }

    /**
     * Check if date string is valid.
     *
     * @param  string $date
     * @access private
     * @return bool
     */
    private function isValidDate($date)
    {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * Adjust date range according to interface limits.
     *
     * @param  string $interfaceType
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return array
     */
    public function adjustDateRange($interfaceType, $startDate, $endDate)
    {
        $limits = $this->getApiTimeLimits();

        if(!isset($limits[$interfaceType]))
        {
            return array('startDate' => $startDate, 'endDate' => $endDate, 'adjusted' => false);
        }

        $limit = $limits[$interfaceType];
        $adjusted = false;

        // For interfaces requiring same date
        if($limit['sameDateRequired'] && $startDate !== $endDate)
        {
            $endDate = $startDate;
            $adjusted = true;
        }

        // Check and adjust time span
        if($this->isValidDate($startDate) && $this->isValidDate($endDate))
        {
            $daysDiff = (strtotime($endDate) - strtotime($startDate)) / 86400;
            if($daysDiff > $limit['maxDays'])
            {
                $endDate = date('Y-m-d', strtotime($startDate . ' +' . $limit['maxDays'] . ' days'));
                $adjusted = true;
            }
        }

        return array('startDate' => $startDate, 'endDate' => $endDate, 'adjusted' => $adjusted);
    }

    /**
     * Get WeChat API instance.
     *
     * @param  int    $publicId
     * @access public
     * @return object|false
     */
    public function getWeChatApi($publicId)
    {
        $public = $this->loadModel('wechat')->getByID($publicId);
        if(empty($public) || empty($public->appID) || empty($public->appSecret))
        {
            return false;
        }

        $config = [
            'app_id' => $public->appID,
            'secret' => $public->appSecret,
            'token' => $public->token,
            'use_stable_access_token' => true,
            'response_type' => 'array',
            'log' => [
                'level' => 'debug',
                'file' => $this->app->getTmpRoot() . 'log/wechat.log',
            ],
        ];

        try {
            require_once('../../../vendor/autoload.php');
            // EasyWeChat 6 initialization
            $app = new \EasyWeChat\OfficialAccount\Application($config);
            return $app;
        } catch (Exception $e) {
            error_log("EasyWeChat initialization failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get overview data for dashboard.
     * 
     * @param  array  $publics 
     * @param  string $startDate 
     * @param  string $endDate 
     * @access public
     * @return array
     */
    public function getOverviewData($publics, $startDate, $endDate)
    {
        $overviewData = array();
        
        foreach($publics as $publicId => $public)
        {
            $data = array();
            $data['public'] = $public;
            
            /* Get user summary */
            $userSummary = $this->getUserSummaryStats($publicId, $startDate, $endDate);
            $data['userSummary'] = $userSummary;
            
            /* Get article summary */
            $articleSummary = $this->getArticleSummaryStats($publicId, $startDate, $endDate);
            $data['articleSummary'] = $articleSummary;
            
            /* Get message summary */
            $messageSummary = $this->getUpstreamMsgStats($publicId, $startDate, $endDate);
            $data['messageSummary'] = $messageSummary;
            
            $overviewData[$publicId] = $data;
        }
        
        return $overviewData;
    }
    
    /**
     * Get user summary statistics.
     * 
     * @param  int    $publicId 
     * @param  string $startDate 
     * @param  string $endDate 
     * @access public
     * @return array
     */
    public function getUserSummaryStats($publicId, $startDate, $endDate)
    {
        $stats = $this->dao->select('
            SUM(new_user) as totalNewUser,
            SUM(cancel_user) as totalCancelUser,
            SUM(new_user - cancel_user) as totalNetUser
        ')
        ->from(TABLE_SMD_WX_USER_SUMMARY)
        ->where('public_id')->eq($publicId)
        ->andWhere('ref_date')->ge($startDate)
        ->andWhere('ref_date')->le($endDate)
        ->fetch();
        
        return $stats ? $stats : (object)array('totalNewUser' => 0, 'totalCancelUser' => 0, 'totalNetUser' => 0);
    }
    
    /**
     * Get article summary statistics.
     * 
     * @param  int    $publicId 
     * @param  string $startDate 
     * @param  string $endDate 
     * @access public
     * @return array
     */
    public function getArticleSummaryStats($publicId, $startDate, $endDate)
    {
        $stats = $this->dao->select('
            SUM(int_page_read_user) as totalReadUser,
            SUM(int_page_read_count) as totalReadCount,
            SUM(share_user) as totalShareUser,
            SUM(share_count) as totalShareCount
        ')
        ->from(TABLE_SMD_WX_ARTICLE_SUMMARY)
        ->where('public_id')->eq($publicId)
        ->andWhere('ref_date')->ge($startDate)
        ->andWhere('ref_date')->le($endDate)
        ->fetch();
        
        return $stats ? $stats : (object)array('totalReadUser' => 0, 'totalReadCount' => 0, 'totalShareUser' => 0, 'totalShareCount' => 0);
    }
    
    /**
     * Get upstream message statistics.
     * 
     * @param  int    $publicId 
     * @param  string $startDate 
     * @param  string $endDate 
     * @access public
     * @return array
     */
    public function getUpstreamMsgStats($publicId, $startDate, $endDate)
    {
        $stats = $this->dao->select('
            SUM(msg_user) as totalMsgUser,
            SUM(msg_count) as totalMsgCount
        ')
        ->from(TABLE_SMD_WX_UPSTREAM_MSG)
        ->where('public_id')->eq($publicId)
        ->andWhere('ref_date')->ge($startDate)
        ->andWhere('ref_date')->le($endDate)
        ->fetch();
        
        return $stats ? $stats : (object)array('totalMsgUser' => 0, 'totalMsgCount' => 0);
    }
    
    /**
     * Get user summary data.
     * 
     * @param  int    $publicId 
     * @param  string $startDate 
     * @param  string $endDate 
     * @access public
     * @return array
     */
    public function getUserSummaryData($publicId, $startDate, $endDate)
    {
        return $this->dao->select('*')
            ->from(TABLE_SMD_WX_USER_SUMMARY)
            ->where('public_id')->eq($publicId)
            ->andWhere('ref_date')->ge($startDate)
            ->andWhere('ref_date')->le($endDate)
            ->orderBy('ref_date, user_source')
            ->fetchAll();
    }
    
    /**
     * Get article summary data.
     * 
     * @param  int    $publicId 
     * @param  string $startDate 
     * @param  string $endDate 
     * @access public
     * @return array
     */
    public function getArticleSummaryData($publicId, $startDate, $endDate)
    {
        return $this->dao->select('*')
            ->from(TABLE_SMD_WX_ARTICLE_SUMMARY)
            ->where('public_id')->eq($publicId)
            ->andWhere('ref_date')->ge($startDate)
            ->andWhere('ref_date')->le($endDate)
            ->orderBy('ref_date DESC, int_page_read_count DESC')
            ->fetchAll();
    }
    
    /**
     * Get upstream message data.
     * 
     * @param  int    $publicId 
     * @param  string $startDate 
     * @param  string $endDate 
     * @access public
     * @return array
     */
    public function getUpstreamMsgData($publicId, $startDate, $endDate)
    {
        return $this->dao->select('*')
            ->from(TABLE_SMD_WX_UPSTREAM_MSG)
            ->where('public_id')->eq($publicId)
            ->andWhere('ref_date')->ge($startDate)
            ->andWhere('ref_date')->le($endDate)
            ->orderBy('ref_date, msg_type')
            ->fetchAll();
    }
    
    /**
     * Get sync logs.
     *
     * @param  int    $publicId
     * @param  string $syncType
     * @param  object $pager
     * @access public
     * @return array
     */
    public function getSyncLogs($publicId = 0, $syncType = '', $pager = null)
    {
        return $this->dao->select('l.*, p.name as publicName')
            ->from(TABLE_SMD_WX_SYNC_LOG)->alias('l')
            ->leftJoin(TABLE_WX_PUBLIC)->alias('p')->on('l.public_id = p.id')
            ->where('1=1')
            ->beginIf($publicId)->andWhere('l.public_id')->eq($publicId)->fi()
            ->beginIf($syncType)->andWhere('l.sync_type')->eq($syncType)->fi()
            ->orderBy('l.created_at DESC')
            ->page($pager)
            ->fetchAll();
    }

    /**
     * Sync user summary data from WeChat API.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncUserSummary($publicId, $startDate, $endDate)
    {
        // Validate date range
        $validation = $this->validateDateRange('user_summary', $startDate, $endDate);
        if(!$validation['result'])
        {
            $this->logSync($publicId, 'user_summary', $startDate, 0, $validation['message']);
            return false;
        }

        $app = $this->getWeChatApi($publicId);
        if(!$app)
        {
            $this->logSync($publicId, 'user_summary', $startDate, 0, 'WeChat API initialization failed');
            return false;
        }

        try
        {
            // EasyWeChat 6 API call
            $params = [
                'begin_date' => $startDate,
                'end_date' => $endDate
            ];
            $response = $app->getClient()->postJson('/datacube/getusersummary', $params);
            $result = $response->toArray();

            if(!is_array($result) || empty($result) || !isset($result['list']))
            {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : 'Invalid API response';
                $this->logSync($publicId, 'user_summary', $startDate, 0, $errorMsg);
                return false;
            }

            $syncCount = 0;
            foreach($result['list'] as $item)
            {
                $data = new stdclass();
                $data->public_id = $publicId;
                $data->ref_date = $item['ref_date'];
                $data->user_source = $item['user_source'];
                $data->new_user = $item['new_user'];
                $data->cancel_user = $item['cancel_user'];

                /* Insert or update */
                $existing = $this->dao->select('id')
                    ->from(TABLE_SMD_WX_USER_SUMMARY)
                    ->where('public_id')->eq($publicId)
                    ->andWhere('ref_date')->eq($item['ref_date'])
                    ->andWhere('user_source')->eq($item['user_source'])
                    ->fetch();

                if($existing)
                {
                    $this->dao->update(TABLE_SMD_WX_USER_SUMMARY)
                        ->data($data)
                        ->where('id')->eq($existing->id)
                        ->exec();
                }
                else
                {
                    $this->dao->insert(TABLE_SMD_WX_USER_SUMMARY)
                        ->data($data)
                        ->exec();
                }

                if(!dao::isError()) $syncCount++;
            }

            $this->logSync($publicId, 'user_summary', $startDate, 1, '', $syncCount);
            return true;
        }
        catch(Exception $e)
        {
            $this->logSync($publicId, 'user_summary', $startDate, 0, $e->getMessage());
            return false;
        }
    }

    /**
     * Sync article summary data from WeChat API.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncArticleSummary($publicId, $startDate, $endDate)
    {
        // Validate date range
        $validation = $this->validateDateRange('article_summary', $startDate, $endDate);
        if(!$validation['result'])
        {
            $this->logSync($publicId, 'article_summary', $startDate, 0, $validation['message']);
            return false;
        }

        // For article_summary, ensure same date
        $endDate = $startDate;

        $app = $this->getWeChatApi($publicId);
        if(!$app)
        {
            $this->logSync($publicId, 'article_summary', $startDate, 0, 'WeChat API initialization failed');
            return false;
        }

        try
        {
            // EasyWeChat 6 API call
            $params = [
                'begin_date' => $startDate,
                'end_date' => $endDate
            ];
            $response = $app->getClient()->postJson('/datacube/getarticlesummary', $params);
            $result = $response->toArray();

            if(!is_array($result) || empty($result) || !isset($result['list']))
            {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : 'Invalid API response';
                $this->logSync($publicId, 'article_summary', $startDate, 0, $errorMsg);
                return false;
            }

            $syncCount = 0;
            foreach($result['list'] as $item)
            {
                $data = new stdclass();
                $data->public_id = $publicId;
                $data->ref_date = $item['ref_date'];
                $data->msgid = $item['msgid'];
                $data->title = isset($item['title']) ? $item['title'] : '';
                $data->int_page_read_user = $item['int_page_read_user'];
                $data->int_page_read_count = $item['int_page_read_count'];
                $data->ori_page_read_user = $item['ori_page_read_user'];
                $data->ori_page_read_count = $item['ori_page_read_count'];
                $data->share_user = $item['share_user'];
                $data->share_count = $item['share_count'];
                $data->add_to_fav_user = $item['add_to_fav_user'];
                $data->add_to_fav_count = $item['add_to_fav_count'];

                /* Insert or update */
                $existing = $this->dao->select('id')
                    ->from(TABLE_SMD_WX_ARTICLE_SUMMARY)
                    ->where('public_id')->eq($publicId)
                    ->andWhere('ref_date')->eq($item['ref_date'])
                    ->andWhere('msgid')->eq($item['msgid'])
                    ->fetch();

                if($existing)
                {
                    $this->dao->update(TABLE_SMD_WX_ARTICLE_SUMMARY)
                        ->data($data)
                        ->where('id')->eq($existing->id)
                        ->exec();
                }
                else
                {
                    $this->dao->insert(TABLE_SMD_WX_ARTICLE_SUMMARY)
                        ->data($data)
                        ->exec();
                }

                if(!dao::isError()) $syncCount++;
            }

            $this->logSync($publicId, 'article_summary', $startDate, 1, '', $syncCount);
            return true;
        }
        catch(Exception $e)
        {
            $this->logSync($publicId, 'article_summary', $startDate, 0, $e->getMessage());
            return false;
        }
    }

    /**
     * Sync upstream message data from WeChat API.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncUpstreamMsg($publicId, $startDate, $endDate)
    {
        // Validate date range
        $validation = $this->validateDateRange('upstream_msg', $startDate, $endDate);
        if(!$validation['result'])
        {
            $this->logSync($publicId, 'upstream_msg', $startDate, 0, $validation['message']);
            return false;
        }

        $app = $this->getWeChatApi($publicId);
        if(!$app)
        {
            $this->logSync($publicId, 'upstream_msg', $startDate, 0, 'WeChat API initialization failed');
            return false;
        }

        try
        {
            // EasyWeChat 6 API call
            $params = [
                'begin_date' => $startDate,
                'end_date' => $endDate
            ];
            $response = $app->getClient()->postJson('/datacube/getupstreammsg', $params);
            $result = $response->toArray();

            if(!is_array($result) || empty($result) || !isset($result['list']))
            {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : 'Invalid API response';
                $this->logSync($publicId, 'upstream_msg', $startDate, 0, $errorMsg);
                return false;
            }

            $syncCount = 0;
            foreach($result['list'] as $item)
            {
                $data = new stdclass();
                $data->public_id = $publicId;
                $data->ref_date = $item['ref_date'];
                $data->msg_type = $item['msg_type'];
                $data->msg_user = $item['msg_user'];
                $data->msg_count = $item['msg_count'];

                /* Insert or update */
                $existing = $this->dao->select('id')
                    ->from(TABLE_SMD_WX_UPSTREAM_MSG)
                    ->where('public_id')->eq($publicId)
                    ->andWhere('ref_date')->eq($item['ref_date'])
                    ->andWhere('msg_type')->eq($item['msg_type'])
                    ->fetch();

                if($existing)
                {
                    $this->dao->update(TABLE_SMD_WX_UPSTREAM_MSG)
                        ->data($data)
                        ->where('id')->eq($existing->id)
                        ->exec();
                }
                else
                {
                    $this->dao->insert(TABLE_SMD_WX_UPSTREAM_MSG)
                        ->data($data)
                        ->exec();
                }

                if(!dao::isError()) $syncCount++;
            }

            $this->logSync($publicId, 'upstream_msg', $startDate, 1, '', $syncCount);
            return true;
        }
        catch(Exception $e)
        {
            $this->logSync($publicId, 'upstream_msg', $startDate, 0, $e->getMessage());
            return false;
        }
    }

    /**
     * Log sync operation.
     *
     * @param  int    $publicId
     * @param  string $syncType
     * @param  string $syncDate
     * @param  int    $status
     * @param  string $errorMsg
     * @param  int    $syncCount
     * @access public
     * @return void
     */
    public function logSync($publicId, $syncType, $syncDate, $status, $errorMsg = '', $syncCount = 0)
    {
        $log = new stdclass();
        $log->public_id = $publicId;
        $log->sync_type = $syncType;
        $log->sync_date = $syncDate;
        $log->status = $status;
        $log->error_msg = $errorMsg;
        $log->sync_count = $syncCount;

        $this->dao->insert(TABLE_SMD_WX_SYNC_LOG)->data($log)->exec();
    }

    /**
     * Sync all data for a public account.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncAllData($publicId, $startDate, $endDate)
    {
        $result1 = $this->syncUserSummary($publicId, $startDate, $endDate);
        $result2 = $this->syncArticleSummary($publicId, $startDate, $endDate);
        $result3 = $this->syncUpstreamMsg($publicId, $startDate, $endDate);

        return $result1 && $result2 && $result3;
    }

    /**
     * Sync data for all public accounts.
     *
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return array
     */
    public function syncAllPublics($startDate, $endDate)
    {
        $publics = $this->loadModel('wechat')->getList();
        $results = array();

        foreach(array_keys($publics) as $publicId)
        {
            $results[$publicId] = $this->syncAllData($publicId, $startDate, $endDate);
        }

        return $results;
    }

    /**
     * Sync user cumulate data from WeChat API.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncUserCumulate($publicId, $startDate, $endDate)
    {
        // Validate date range
        $validation = $this->validateDateRange('user_cumulate', $startDate, $endDate);
        if(!$validation['result'])
        {
            $this->logSync($publicId, 'user_cumulate', $startDate, 0, $validation['message']);
            return false;
        }

        $app = $this->getWeChatApi($publicId);
        if(!$app)
        {
            $this->logSync($publicId, 'user_cumulate', $startDate, 0, 'WeChat API initialization failed');
            return false;
        }

        try
        {
            // EasyWeChat 6 API call
            $params = [
                'begin_date' => $startDate,
                'end_date' => $endDate
            ];
            $response = $app->getClient()->postJson('/datacube/getusercumulate', $params);
            $result = $response->toArray();

            if(!is_array($result) || empty($result) || !isset($result['list']))
            {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : 'Invalid API response';
                $this->logSync($publicId, 'user_cumulate', $startDate, 0, $errorMsg);
                return false;
            }

            $syncCount = 0;
            foreach($result['list'] as $item)
            {
                $data = new stdclass();
                $data->public_id = $publicId;
                $data->ref_date = $item['ref_date'];
                $data->cumulate_user = $item['cumulate_user'];

                /* Insert or update */
                $existing = $this->dao->select('id')
                    ->from(TABLE_SMD_WX_USER_CUMULATE)
                    ->where('public_id')->eq($publicId)
                    ->andWhere('ref_date')->eq($item['ref_date'])
                    ->fetch();

                if($existing)
                {
                    $this->dao->update(TABLE_SMD_WX_USER_CUMULATE)
                        ->data($data)
                        ->where('id')->eq($existing->id)
                        ->exec();
                }
                else
                {
                    $this->dao->insert(TABLE_SMD_WX_USER_CUMULATE)
                        ->data($data)
                        ->exec();
                }

                if(!dao::isError()) $syncCount++;
            }

            $this->logSync($publicId, 'user_cumulate', $startDate, 1, '', $syncCount);
            return true;
        }
        catch(Exception $e)
        {
            $this->logSync($publicId, 'user_cumulate', $startDate, 0, $e->getMessage());
            return false;
        }
    }

    /**
     * Sync user read hour data from WeChat API.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncUserReadHour($publicId, $startDate, $endDate)
    {
        // Validate date range
        $validation = $this->validateDateRange('user_read_hour', $startDate, $endDate);
        if(!$validation['result'])
        {
            $this->logSync($publicId, 'user_read_hour', $startDate, 0, $validation['message']);
            return false;
        }

        // For user_read_hour, ensure same date
        $endDate = $startDate;

        $app = $this->getWeChatApi($publicId);
        if(!$app)
        {
            $this->logSync($publicId, 'user_read_hour', $startDate, 0, 'WeChat API initialization failed');
            return false;
        }

        try
        {
            // EasyWeChat 6 API call
            $params = [
                'begin_date' => $startDate,
                'end_date' => $endDate
            ];
            $response = $app->getClient()->postJson('/datacube/getuserreadhour', $params);
            $result = $response->toArray();

            if(!is_array($result) || empty($result) || !isset($result['list']))
            {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : 'Invalid API response';
                $this->logSync($publicId, 'user_read_hour', $startDate, 0, $errorMsg);
                return false;
            }

            $syncCount = 0;
            foreach($result['list'] as $item)
            {
                $data = new stdclass();
                $data->public_id = $publicId;
                $data->ref_date = $item['ref_date'];
                $data->ref_hour = $item['ref_hour'];
                $data->user_source = $item['user_source'];
                $data->int_page_read_user = $item['int_page_read_user'];
                $data->int_page_read_count = $item['int_page_read_count'];
                $data->ori_page_read_user = $item['ori_page_read_user'];
                $data->ori_page_read_count = $item['ori_page_read_count'];
                $data->share_user = $item['share_user'];
                $data->share_count = $item['share_count'];
                $data->add_to_fav_user = $item['add_to_fav_user'];
                $data->add_to_fav_count = $item['add_to_fav_count'];

                /* Insert or update */
                $existing = $this->dao->select('id')
                    ->from(TABLE_SMD_WX_USER_READ_HOUR)
                    ->where('public_id')->eq($publicId)
                    ->andWhere('ref_date')->eq($item['ref_date'])
                    ->andWhere('ref_hour')->eq($item['ref_hour'])
                    ->andWhere('user_source')->eq($item['user_source'])
                    ->fetch();

                if($existing)
                {
                    $this->dao->update(TABLE_SMD_WX_USER_READ_HOUR)
                        ->data($data)
                        ->where('id')->eq($existing->id)
                        ->exec();
                }
                else
                {
                    $this->dao->insert(TABLE_SMD_WX_USER_READ_HOUR)
                        ->data($data)
                        ->exec();
                }

                if(!dao::isError()) $syncCount++;
            }

            $this->logSync($publicId, 'user_read_hour', $startDate, 1, '', $syncCount);
            return true;
        }
        catch(Exception $e)
        {
            $this->logSync($publicId, 'user_read_hour', $startDate, 0, $e->getMessage());
            return false;
        }
    }

    /**
     * Sync user share hour data from WeChat API.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncUserShareHour($publicId, $startDate, $endDate)
    {
        // Validate date range
        $validation = $this->validateDateRange('user_share_hour', $startDate, $endDate);
        if(!$validation['result'])
        {
            $this->logSync($publicId, 'user_share_hour', $startDate, 0, $validation['message']);
            return false;
        }

        // For user_share_hour, ensure same date
        $endDate = $startDate;

        $app = $this->getWeChatApi($publicId);
        if(!$app)
        {
            $this->logSync($publicId, 'user_share_hour', $startDate, 0, 'WeChat API initialization failed');
            return false;
        }

        try
        {
            // EasyWeChat 6 API call
            $params = [
                'begin_date' => $startDate,
                'end_date' => $endDate
            ];
            $response = $app->getClient()->postJson('/datacube/getusersharehour', $params);
            $result = $response->toArray();

            if(!is_array($result) || empty($result) || !isset($result['list']))
            {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : 'Invalid API response';
                $this->logSync($publicId, 'user_share_hour', $startDate, 0, $errorMsg);
                return false;
            }

            $syncCount = 0;
            foreach($result['list'] as $item)
            {
                $data = new stdclass();
                $data->public_id = $publicId;
                $data->ref_date = $item['ref_date'];
                $data->ref_hour = $item['ref_hour'];
                $data->share_user = $item['share_user'];
                $data->share_count = $item['share_count'];
                $data->share_scene = $item['share_scene'];

                /* Insert or update */
                $existing = $this->dao->select('id')
                    ->from(TABLE_SMD_WX_USER_SHARE_HOUR)
                    ->where('public_id')->eq($publicId)
                    ->andWhere('ref_date')->eq($item['ref_date'])
                    ->andWhere('ref_hour')->eq($item['ref_hour'])
                    ->andWhere('share_scene')->eq($item['share_scene'])
                    ->fetch();

                if($existing)
                {
                    $this->dao->update(TABLE_SMD_WX_USER_SHARE_HOUR)
                        ->data($data)
                        ->where('id')->eq($existing->id)
                        ->exec();
                }
                else
                {
                    $this->dao->insert(TABLE_SMD_WX_USER_SHARE_HOUR)
                        ->data($data)
                        ->exec();
                }

                if(!dao::isError()) $syncCount++;
            }

            $this->logSync($publicId, 'user_share_hour', $startDate, 1, '', $syncCount);
            return true;
        }
        catch(Exception $e)
        {
            $this->logSync($publicId, 'user_share_hour', $startDate, 0, $e->getMessage());
            return false;
        }
    }

    /**
     * Sync user read data from WeChat API.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncUserRead($publicId, $startDate, $endDate)
    {
        // Validate date range
        $validation = $this->validateDateRange('user_read', $startDate, $endDate);
        if(!$validation['result'])
        {
            $this->logSync($publicId, 'user_read', $startDate, 0, $validation['message']);
            return false;
        }

        $app = $this->getWeChatApi($publicId);
        if(!$app)
        {
            $this->logSync($publicId, 'user_read', $startDate, 0, 'WeChat API initialization failed');
            return false;
        }

        try
        {
            // EasyWeChat 6 API call
            $params = [
                'begin_date' => $startDate,
                'end_date' => $endDate
            ];
            $response = $app->getClient()->postJson('/datacube/getuserread', $params);
            $result = $response->toArray();

            if(!is_array($result) || empty($result) || !isset($result['list']))
            {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : 'Invalid API response';
                $this->logSync($publicId, 'user_read', $startDate, 0, $errorMsg);
                return false;
            }

            $syncCount = 0;
            foreach($result['list'] as $item)
            {
                $data = new stdclass();
                $data->public_id = $publicId;
                $data->ref_date = $item['ref_date'];
                $data->user_source = $item['user_source'];
                $data->int_page_read_user = $item['int_page_read_user'];
                $data->int_page_read_count = $item['int_page_read_count'];
                $data->ori_page_read_user = $item['ori_page_read_user'];
                $data->ori_page_read_count = $item['ori_page_read_count'];
                $data->share_user = $item['share_user'];
                $data->share_count = $item['share_count'];
                $data->add_to_fav_user = $item['add_to_fav_user'];
                $data->add_to_fav_count = $item['add_to_fav_count'];

                /* Insert or update */
                $existing = $this->dao->select('id')
                    ->from(TABLE_SMD_WX_USER_READ)
                    ->where('public_id')->eq($publicId)
                    ->andWhere('ref_date')->eq($item['ref_date'])
                    ->andWhere('user_source')->eq($item['user_source'])
                    ->fetch();

                if($existing)
                {
                    $this->dao->update(TABLE_SMD_WX_USER_READ)
                        ->data($data)
                        ->where('id')->eq($existing->id)
                        ->exec();
                }
                else
                {
                    $this->dao->insert(TABLE_SMD_WX_USER_READ)
                        ->data($data)
                        ->exec();
                }

                if(!dao::isError()) $syncCount++;
            }

            $this->logSync($publicId, 'user_read', $startDate, 1, '', $syncCount);
            return true;
        }
        catch(Exception $e)
        {
            $this->logSync($publicId, 'user_read', $startDate, 0, $e->getMessage());
            return false;
        }
    }

    /**
     * Sync article total data from WeChat API.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncArticleTotal($publicId, $startDate, $endDate)
    {
        // Validate date range
        $validation = $this->validateDateRange('article_total', $startDate, $endDate);
        if(!$validation['result'])
        {
            $this->logSync($publicId, 'article_total', $startDate, 0, $validation['message']);
            return false;
        }

        // For article_total, ensure same date
        $endDate = $startDate;

        $app = $this->getWeChatApi($publicId);
        if(!$app)
        {
            $this->logSync($publicId, 'article_total', $startDate, 0, 'WeChat API initialization failed');
            return false;
        }

        try
        {
            // EasyWeChat 6 API call
            $params = [
                'begin_date' => $startDate,
                'end_date' => $endDate
            ];
            $response = $app->getClient()->postJson('/datacube/getarticletotal', $params);
            $result = $response->toArray();

            if(!is_array($result) || empty($result) || !isset($result['list']))
            {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : 'Invalid API response';
                $this->logSync($publicId, 'article_total', $startDate, 0, $errorMsg);
                return false;
            }

            $syncCount = 0;
            foreach($result['list'] as $item)
            {
                $data = new stdclass();
                $data->public_id = $publicId;
                $data->ref_date = $item['ref_date'];
                $data->stat_date = $item['stat_date'];
                $data->msgid = $item['msgid'];
                $data->title = $item['title'];
                $data->target_user = $item['target_user'];
                $data->int_page_read_user = $item['int_page_read_user'];
                $data->int_page_read_count = $item['int_page_read_count'];
                $data->ori_page_read_user = $item['ori_page_read_user'];
                $data->ori_page_read_count = $item['ori_page_read_count'];
                $data->share_user = $item['share_user'];
                $data->share_count = $item['share_count'];
                $data->add_to_fav_user = $item['add_to_fav_user'];
                $data->add_to_fav_count = $item['add_to_fav_count'];
                $data->int_page_from_session_read_user = $item['int_page_from_session_read_user'];
                $data->int_page_from_session_read_count = $item['int_page_from_session_read_count'];
                $data->int_page_from_hist_msg_read_user = $item['int_page_from_hist_msg_read_user'];
                $data->int_page_from_hist_msg_read_count = $item['int_page_from_hist_msg_read_count'];
                $data->int_page_from_feed_read_user = $item['int_page_from_feed_read_user'];
                $data->int_page_from_feed_read_count = $item['int_page_from_feed_read_count'];
                $data->int_page_from_friends_read_user = $item['int_page_from_friends_read_user'];
                $data->int_page_from_friends_read_count = $item['int_page_from_friends_read_count'];
                $data->int_page_from_other_read_user = $item['int_page_from_other_read_user'];
                $data->int_page_from_other_read_count = $item['int_page_from_other_read_count'];
                $data->feed_share_from_session_user = $item['feed_share_from_session_user'];
                $data->feed_share_from_session_cnt = $item['feed_share_from_session_cnt'];
                $data->feed_share_from_feed_user = $item['feed_share_from_feed_user'];
                $data->feed_share_from_feed_cnt = $item['feed_share_from_feed_cnt'];
                $data->feed_share_from_other_user = $item['feed_share_from_other_user'];
                $data->feed_share_from_other_cnt = $item['feed_share_from_other_cnt'];
                $data->int_page_from_kanyikan_read_user = $item['int_page_from_kanyikan_read_user'];
                $data->int_page_from_kanyikan_read_count = $item['int_page_from_kanyikan_read_count'];
                $data->int_page_from_souyisou_read_user = $item['int_page_from_souyisou_read_user'];
                $data->int_page_from_souyisou_read_count = $item['int_page_from_souyisou_read_count'];

                /* Insert or update */
                $existing = $this->dao->select('id')
                    ->from(TABLE_SMD_WX_ARTICLE_TOTAL)
                    ->where('public_id')->eq($publicId)
                    ->andWhere('ref_date')->eq($item['ref_date'])
                    ->andWhere('stat_date')->eq($item['stat_date'])
                    ->andWhere('msgid')->eq($item['msgid'])
                    ->fetch();

                if($existing)
                {
                    $this->dao->update(TABLE_SMD_WX_ARTICLE_TOTAL)
                        ->data($data)
                        ->where('id')->eq($existing->id)
                        ->exec();
                }
                else
                {
                    $this->dao->insert(TABLE_SMD_WX_ARTICLE_TOTAL)
                        ->data($data)
                        ->exec();
                }

                if(!dao::isError()) $syncCount++;
            }

            $this->logSync($publicId, 'article_total', $startDate, 1, '', $syncCount);
            return true;
        }
        catch(Exception $e)
        {
            $this->logSync($publicId, 'article_total', $startDate, 0, $e->getMessage());
            return false;
        }
    }

    /**
     * Sync user share data from WeChat API.
     *
     * @param  int    $publicId
     * @param  string $startDate
     * @param  string $endDate
     * @access public
     * @return bool
     */
    public function syncUserShare($publicId, $startDate, $endDate)
    {
        // Validate date range
        $validation = $this->validateDateRange('user_share', $startDate, $endDate);
        if(!$validation['result'])
        {
            $this->logSync($publicId, 'user_share', $startDate, 0, $validation['message']);
            return false;
        }

        $app = $this->getWeChatApi($publicId);
        if(!$app)
        {
            $this->logSync($publicId, 'user_share', $startDate, 0, 'WeChat API initialization failed');
            return false;
        }

        try
        {
            // EasyWeChat 6 API call
            $params = [
                'begin_date' => $startDate,
                'end_date' => $endDate
            ];
            $response = $app->getClient()->postJson('/datacube/getusershare', $params);
            $result = $response->toArray();

            if(!is_array($result) || empty($result) || !isset($result['list']))
            {
                $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : 'Invalid API response';
                $this->logSync($publicId, 'user_share', $startDate, 0, $errorMsg);
                return false;
            }

            $syncCount = 0;
            foreach($result['list'] as $item)
            {
                $data = new stdclass();
                $data->public_id = $publicId;
                $data->ref_date = $item['ref_date'];
                $data->share_scene = $item['share_scene'];
                $data->share_count = $item['share_count'];
                $data->share_user = $item['share_user'];

                /* Insert or update */
                $existing = $this->dao->select('id')
                    ->from(TABLE_SMD_WX_USER_SHARE)
                    ->where('public_id')->eq($publicId)
                    ->andWhere('ref_date')->eq($item['ref_date'])
                    ->andWhere('share_scene')->eq($item['share_scene'])
                    ->fetch();

                if($existing)
                {
                    $this->dao->update(TABLE_SMD_WX_USER_SHARE)
                        ->data($data)
                        ->where('id')->eq($existing->id)
                        ->exec();
                }
                else
                {
                    $this->dao->insert(TABLE_SMD_WX_USER_SHARE)
                        ->data($data)
                        ->exec();
                }

                if(!dao::isError()) $syncCount++;
            }

            $this->logSync($publicId, 'user_share', $startDate, 1, '', $syncCount);
            return true;
        }
        catch(Exception $e)
        {
            $this->logSync($publicId, 'user_share', $startDate, 0, $e->getMessage());
            return false;
        }
    }
}
