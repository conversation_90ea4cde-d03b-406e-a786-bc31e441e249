-- 微信数据统计模块新增数据表
-- WeChat Data Statistics Module New Tables

-- 用户累计数据表
CREATE TABLE IF NOT EXISTS `eps_wx_user_cumulate` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint unsigned NOT NULL COMMENT '公众号ID',
  `ref_date` date NOT NULL COMMENT '数据的日期',
  `cumulate_user` int NOT NULL DEFAULT '0' COMMENT '总用户量',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_data` (`public_id`, `ref_date`),
  KEY `idx_public_date` (`public_id`, `ref_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户累计数据表';

-- 图文阅读分时数据表
CREATE TABLE IF NOT EXISTS `eps_wx_user_read_hour` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint unsigned NOT NULL COMMENT '公众号ID',
  `ref_date` date NOT NULL COMMENT '数据的日期',
  `ref_hour` int NOT NULL COMMENT '数据小时',
  `user_source` int NOT NULL COMMENT '用户从哪里进入来阅读该图文。99999999.全部；0:会话;1.好友;2.朋友圈;4.历史消息页;5.其他;6.看一看;7.搜一搜',
  `int_page_read_user` int NOT NULL DEFAULT '0' COMMENT '图文页阅读人数',
  `int_page_read_count` int NOT NULL DEFAULT '0' COMMENT '图文页阅读次数',
  `ori_page_read_user` int NOT NULL DEFAULT '0' COMMENT '原文页阅读人数',
  `ori_page_read_count` int NOT NULL DEFAULT '0' COMMENT '原文页阅读次数',
  `share_user` int NOT NULL DEFAULT '0' COMMENT '分享的人数',
  `share_count` int NOT NULL DEFAULT '0' COMMENT '分享的次数',
  `add_to_fav_user` int NOT NULL DEFAULT '0' COMMENT '收藏的人数',
  `add_to_fav_count` int NOT NULL DEFAULT '0' COMMENT '收藏的次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_data` (`public_id`, `ref_date`, `ref_hour`, `user_source`),
  KEY `idx_public_date` (`public_id`, `ref_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信图文阅读分时数据表';

-- 图文分享分时数据表
CREATE TABLE IF NOT EXISTS `eps_wx_user_share_hour` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint unsigned NOT NULL COMMENT '公众号ID',
  `ref_date` date NOT NULL COMMENT '数据的日期',
  `ref_hour` int NOT NULL COMMENT '数据小时',
  `share_user` int NOT NULL DEFAULT '0' COMMENT '分享的人数',
  `share_count` int NOT NULL DEFAULT '0' COMMENT '分享的次数',
  `share_scene` tinyint NOT NULL COMMENT '分享的场景 1代表好友转发 2代表朋友圈 255代表其他',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_data` (`public_id`, `ref_date`, `ref_hour`, `share_scene`),
  KEY `idx_public_date` (`public_id`, `ref_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信图文分享分时数据表';

-- 图文统计数据表
CREATE TABLE IF NOT EXISTS `eps_wx_user_read` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint unsigned NOT NULL COMMENT '公众号ID',
  `ref_date` date NOT NULL COMMENT '数据的日期',
  `user_source` int NOT NULL COMMENT '用户从哪里进入来阅读该图文。99999999.全部；0:会话;1.好友;2.朋友圈;4.历史消息页;5.其他;6.看一看;7.搜一搜',
  `int_page_read_user` int NOT NULL DEFAULT '0' COMMENT '图文页阅读人数',
  `int_page_read_count` int NOT NULL DEFAULT '0' COMMENT '图文页阅读次数',
  `ori_page_read_user` int NOT NULL DEFAULT '0' COMMENT '原文页阅读人数',
  `ori_page_read_count` int NOT NULL DEFAULT '0' COMMENT '原文页阅读次数',
  `share_user` int NOT NULL DEFAULT '0' COMMENT '分享的人数',
  `share_count` int NOT NULL DEFAULT '0' COMMENT '分享的次数',
  `add_to_fav_user` int NOT NULL DEFAULT '0' COMMENT '收藏的人数',
  `add_to_fav_count` int NOT NULL DEFAULT '0' COMMENT '收藏的次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_data` (`public_id`, `ref_date`, `user_source`),
  KEY `idx_public_date` (`public_id`, `ref_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信图文统计数据表';

-- 图文群发总数据表
CREATE TABLE IF NOT EXISTS `eps_wx_article_total` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint unsigned NOT NULL COMMENT '公众号ID',
  `ref_date` date NOT NULL COMMENT '文章群发出日期',
  `stat_date` date NOT NULL COMMENT '数据统计日期',
  `msgid` varchar(64) NOT NULL COMMENT '消息id',
  `title` varchar(255) NOT NULL COMMENT '图文消息的标题',
  `target_user` int NOT NULL DEFAULT '0' COMMENT '送达人数',
  `int_page_read_user` int NOT NULL DEFAULT '0' COMMENT '图文页阅读人数',
  `int_page_read_count` int NOT NULL DEFAULT '0' COMMENT '图文页阅读次数',
  `ori_page_read_user` int NOT NULL DEFAULT '0' COMMENT '原文页阅读人数',
  `ori_page_read_count` int NOT NULL DEFAULT '0' COMMENT '原文页阅读次数',
  `share_user` int NOT NULL DEFAULT '0' COMMENT '分享的人数',
  `share_count` int NOT NULL DEFAULT '0' COMMENT '分享的次数',
  `add_to_fav_user` int NOT NULL DEFAULT '0' COMMENT '收藏的人数',
  `add_to_fav_count` int NOT NULL DEFAULT '0' COMMENT '收藏的次数',
  `int_page_from_session_read_user` int NOT NULL DEFAULT '0' COMMENT '公众号会话阅读人数',
  `int_page_from_session_read_count` int NOT NULL DEFAULT '0' COMMENT '公众号会话阅读次数',
  `int_page_from_hist_msg_read_user` int NOT NULL DEFAULT '0' COMMENT '历史消息页阅读人数',
  `int_page_from_hist_msg_read_count` int NOT NULL DEFAULT '0' COMMENT '历史消息页阅读次数',
  `int_page_from_feed_read_user` int NOT NULL DEFAULT '0' COMMENT '朋友圈阅读人数',
  `int_page_from_feed_read_count` int NOT NULL DEFAULT '0' COMMENT '朋友圈阅读次数',
  `int_page_from_friends_read_user` int NOT NULL DEFAULT '0' COMMENT '好友转发阅读人数',
  `int_page_from_friends_read_count` int NOT NULL DEFAULT '0' COMMENT '好友转发阅读次数',
  `int_page_from_other_read_user` int NOT NULL DEFAULT '0' COMMENT '其他场景阅读人数',
  `int_page_from_other_read_count` int NOT NULL DEFAULT '0' COMMENT '其他场景阅读次数',
  `feed_share_from_session_user` int NOT NULL DEFAULT '0' COMMENT '公众号会话转发朋友圈人数',
  `feed_share_from_session_cnt` int NOT NULL DEFAULT '0' COMMENT '公众号会话转发朋友圈次数',
  `feed_share_from_feed_user` int NOT NULL DEFAULT '0' COMMENT '朋友圈转发朋友圈人数',
  `feed_share_from_feed_cnt` int NOT NULL DEFAULT '0' COMMENT '朋友圈转发朋友圈次数',
  `feed_share_from_other_user` int NOT NULL DEFAULT '0' COMMENT '其他场景转发朋友圈人数',
  `feed_share_from_other_cnt` int NOT NULL DEFAULT '0' COMMENT '其他场景转发朋友圈次数',
  `int_page_from_kanyikan_read_user` int NOT NULL DEFAULT '0' COMMENT '看一看来源阅读人数',
  `int_page_from_kanyikan_read_count` int NOT NULL DEFAULT '0' COMMENT '看一看来源阅读次数',
  `int_page_from_souyisou_read_user` int NOT NULL DEFAULT '0' COMMENT '搜一搜来源阅读人数',
  `int_page_from_souyisou_read_count` int NOT NULL DEFAULT '0' COMMENT '搜一搜来源阅读次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_data` (`public_id`, `ref_date`, `stat_date`, `msgid`),
  KEY `idx_public_date` (`public_id`, `ref_date`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信图文群发总数据表';

-- 图文分享转发数据表
CREATE TABLE IF NOT EXISTS `eps_wx_user_share` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `public_id` smallint unsigned NOT NULL COMMENT '公众号ID',
  `ref_date` date NOT NULL COMMENT '数据的日期',
  `share_scene` tinyint NOT NULL COMMENT '分享的场景，1代表好友转发 2代表朋友圈 3代表腾讯微博 255代表其他',
  `share_count` int NOT NULL DEFAULT '0' COMMENT '分享的次数',
  `share_user` int NOT NULL DEFAULT '0' COMMENT '分享的人数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_data` (`public_id`, `ref_date`, `share_scene`),
  KEY `idx_public_date` (`public_id`, `ref_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信图文分享转发数据表';
